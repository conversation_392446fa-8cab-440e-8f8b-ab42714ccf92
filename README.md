# Restaurant Management System

A comprehensive restaurant management application built on ERPNext/Frappe Framework. This app demonstrates key ERPNext concepts including DocTypes, controllers, client-side scripting, reports, and integrations.

## 🚀 Features

### Restaurant Operations
- **Table Management**: Track table status, capacity, and location
- **Order Management**: Complete order lifecycle from creation to payment
- **Reservation System**: Advanced table booking with conflict detection
- **Real-time Status Updates**: Live updates of table and order status

### Menu Management
- **Menu Items**: Comprehensive item management with pricing and categories
- **Profit Margin Calculation**: Automatic profit margin calculations
- **Availability Control**: Real-time menu item availability management
- **Rich Item Details**: Images, ingredients, allergens, and nutritional info

### Analytics & Reporting
- **Daily Sales Summary**: Comprehensive sales analytics with charts
- **Menu Performance**: Track best-selling items and profitability
- **Table Utilization**: Monitor table efficiency and revenue
- **Custom Dashboards**: Visual insights into restaurant performance

## 📋 Learning Objectives

This app is designed to teach you:

1. **ERPNext App Structure**: Understanding modules, doctypes, and organization
2. **DocType Design**: Creating master data and transaction documents
3. **Python Controllers**: Business logic, validations, and workflows
4. **Client-side Scripting**: JavaScript for dynamic user interfaces
5. **Reports & Analytics**: Both query reports and script reports
6. **Integration Patterns**: How different doctypes work together
7. **Testing**: Unit tests and test-driven development
8. **Best Practices**: Code organization, naming conventions, and documentation

## 🏗️ Architecture Overview

### Modules
- **Restaurant Operations**: Core operational functionality
- **Menu Management**: Menu and item management

### Key DocTypes
- **Restaurant Table**: Master data for restaurant tables
- **Menu Item**: Master data for menu items
- **Restaurant Order**: Transaction document for orders
- **Table Reservation**: Transaction document for reservations
- **Restaurant Order Item**: Child table for order line items

### Integration Flow
```
Table Reservation → Restaurant Table → Restaurant Order → Menu Item
                                   ↓
                            Restaurant Order Item
```

## 🔧 Installation & Setup

You can install this app using the [bench](https://github.com/frappe/bench) CLI:

```bash
cd $PATH_TO_YOUR_BENCH
bench get-app $URL_OF_THIS_REPO --branch develop
bench install-app restaurant_management
```

### Manual Installation Steps
1. **Install on site**:
   ```bash
   bench --site [site-name] install-app restaurant_management
   ```

2. **Run migrations**:
   ```bash
   bench --site [site-name] migrate
   ```

3. **Build assets**:
   ```bash
   bench build --app restaurant_management
   ```

### Contributing

This app uses `pre-commit` for code formatting and linting. Please [install pre-commit](https://pre-commit.com/#installation) and enable it for this repository:

```bash
cd apps/restaurant_management
pre-commit install
```

Pre-commit is configured to use the following tools for checking and formatting your code:

- ruff
- eslint
- prettier
- pyupgrade

### CI

This app can use GitHub Actions for CI. The following workflows are configured:

- CI: Installs this app and runs unit tests on every push to `develop` branch.
- Linters: Runs [Frappe Semgrep Rules](https://github.com/frappe/semgrep-rules) and [pip-audit](https://pypi.org/project/pip-audit/) on every pull request.


### License

mit
