{"add_total_row": 1, "columns": [], "creation": "2024-01-01 00:00:00.000000", "docstatus": 0, "doctype": "Report", "filters": [], "idx": 0, "is_standard": "Yes", "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "Menu Management", "name": "Menu Performance", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "<PERSON><PERSON>", "report_name": "Menu Performance", "report_type": "Query Report", "roles": [{"role": "System Manager"}, {"role": "Restaurant Manager"}], "query": "SELECT \n    mi.item_name as \"Item Name:Data:150\",\n    mi.category as \"Category:Data:100\",\n    mi.price as \"Price:Currency:100\",\n    mi.cost_price as \"Cost Price:Currency:100\",\n    mi.profit_margin as \"Profit Margin:Percent:120\",\n    COALESCE(SUM(roi.quantity), 0) as \"Total Sold:Int:100\",\n    COALESCE(SUM(roi.amount), 0) as \"Total Revenue:Currency:120\",\n    COALESCE(COUNT(DISTINCT ro.name), 0) as \"Orders Count:Int:100\",\n    CASE \n        WHEN mi.is_available = 1 THEN 'Available'\n        ELSE 'Unavailable'\n    END as \"Status:Data:100\",\n    mi.preparation_time as \"Prep Time:Int:100\"\nFROM `tabMenu Item` mi\nLEFT JOIN `tabRestaurant Order Item` roi ON mi.name = roi.menu_item\nLEFT JOIN `tabRestaurant Order` ro ON roi.parent = ro.name AND ro.docstatus != 2 AND ro.status = 'Paid'\nGROUP BY mi.name, mi.item_name, mi.category, mi.price, mi.cost_price, mi.profit_margin, mi.is_available, mi.preparation_time\nORDER BY COALESCE(SUM(roi.quantity), 0) DESC"}