# Copyright (c) 2024, anik and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now_datetime, getdate, get_time, add_days
from datetime import datetime, timedelta


class TableReservation(Document):
	"""
	Table Reservation DocType Controller
	
	This class handles table reservation management including:
	- Availability checking
	- Automatic table assignment
	- Reservation workflow
	- Customer notifications
	"""
	
	def validate(self):
		"""Validate reservation data"""
		self.validate_reservation_datetime()
		self.validate_party_size()
		self.check_table_availability()
	
	def validate_reservation_datetime(self):
		"""Validate reservation date and time"""
		reservation_datetime = datetime.combine(
			getdate(self.reservation_date),
			get_time(self.reservation_time)
		)
		
		# Check if reservation is in the past
		if reservation_datetime < datetime.now():
			frappe.throw("Reservation cannot be made for past date/time")
		
		# Check if reservation is too far in the future (e.g., 30 days)
		max_advance_days = frappe.db.get_single_value("Restaurant Settings", "max_advance_reservation_days") or 30
		if reservation_datetime > datetime.now() + timedelta(days=max_advance_days):
			frappe.throw(f"Reservations can only be made up to {max_advance_days} days in advance")
	
	def validate_party_size(self):
		"""Validate party size"""
		if self.party_size <= 0:
			frappe.throw("Party size must be greater than 0")
		
		if self.party_size > 20:
			frappe.msgprint("Large party size detected. Please verify availability.", alert=True)
	
	def check_table_availability(self):
		"""Check if requested table is available"""
		if self.table:
			# Check if table has sufficient capacity
			table_doc = frappe.get_doc("Restaurant Table", self.table)
			if table_doc.capacity < self.party_size:
				frappe.throw(f"Table {self.table} has capacity of {table_doc.capacity}, but party size is {self.party_size}")
			
			# Check for conflicting reservations
			conflicting_reservations = self.get_conflicting_reservations()
			if conflicting_reservations:
				frappe.throw(f"Table {self.table} is already reserved for the requested time slot")
	
	def get_conflicting_reservations(self):
		"""Get conflicting reservations for the same table and time"""
		# Define time buffer (e.g., 2 hours)
		time_buffer = timedelta(hours=2)
		
		reservation_datetime = datetime.combine(
			getdate(self.reservation_date),
			get_time(self.reservation_time)
		)
		
		start_time = reservation_datetime - time_buffer
		end_time = reservation_datetime + time_buffer
		
		filters = {
			"table": self.table,
			"status": ["in", ["Confirmed", "Seated"]],
			"name": ["!=", self.name] if not self.is_new() else ["!=", ""]
		}
		
		conflicting = frappe.db.sql("""
			SELECT name, customer_name, reservation_date, reservation_time
			FROM `tabTable Reservation`
			WHERE table = %(table)s
			AND status IN ('Confirmed', 'Seated')
			AND name != %(current_name)s
			AND TIMESTAMP(reservation_date, reservation_time) BETWEEN %(start_time)s AND %(end_time)s
		""", {
			"table": self.table,
			"current_name": self.name or "",
			"start_time": start_time,
			"end_time": end_time
		}, as_dict=True)
		
		return conflicting
	
	def before_save(self):
		"""Actions before saving"""
		if not self.created_by:
			self.created_by = frappe.session.user
		if not self.created_on:
			self.created_on = now_datetime()
	
	def on_update(self):
		"""Actions after updating"""
		if self.has_value_changed("status"):
			self.handle_status_change()
	
	def handle_status_change(self):
		"""Handle reservation status changes"""
		if self.status == "Confirmed":
			self.confirmed_by = frappe.session.user
			self.confirmed_on = now_datetime()
			self.reserve_table()
			self.send_confirmation_notification()
		
		elif self.status == "Seated":
			self.mark_table_occupied()
		
		elif self.status in ["Cancelled", "No Show"]:
			self.free_table()
	
	def reserve_table(self):
		"""Reserve the table"""
		if self.table:
			table_doc = frappe.get_doc("Restaurant Table", self.table)
			reservation_details = f"Reserved for {self.customer_name} on {self.reservation_date} at {self.reservation_time}"
			table_doc.set_reserved(reservation_details)
	
	def mark_table_occupied(self):
		"""Mark table as occupied when customer is seated"""
		if self.table:
			table_doc = frappe.get_doc("Restaurant Table", self.table)
			table_doc.set_occupied()
	
	def free_table(self):
		"""Free the table"""
		if self.table:
			table_doc = frappe.get_doc("Restaurant Table", self.table)
			table_doc.set_available()
	
	def send_confirmation_notification(self):
		"""Send confirmation notification to customer"""
		# This is a placeholder for email/SMS integration
		frappe.msgprint(f"Confirmation sent to {self.customer_name} at {self.phone_number}", alert=True)
	
	@frappe.whitelist()
	def confirm_reservation(self):
		"""Confirm the reservation"""
		self.status = "Confirmed"
		self.save()
		return self.status
	
	@frappe.whitelist()
	def mark_seated(self):
		"""Mark customer as seated"""
		self.status = "Seated"
		self.save()
		return self.status
	
	@frappe.whitelist()
	def mark_no_show(self):
		"""Mark as no show"""
		self.status = "No Show"
		self.save()
		return self.status
	
	@frappe.whitelist()
	def cancel_reservation(self, reason=None):
		"""Cancel the reservation"""
		self.status = "Cancelled"
		if reason:
			self.notes = (self.notes or "") + f"\nCancellation reason: {reason}"
		self.save()
		return self.status
	
	@frappe.whitelist()
	def suggest_alternative_tables(self):
		"""Suggest alternative tables if requested table is not available"""
		from restaurant_management.restaurant_operations.doctype.restaurant_table.restaurant_table import get_available_tables
		
		# Get tables with sufficient capacity
		available_tables = get_available_tables(min_capacity=self.party_size)
		
		# Filter out tables that have conflicting reservations
		suitable_tables = []
		for table in available_tables:
			# Temporarily set table to check conflicts
			original_table = self.table
			self.table = table["name"]
			
			if not self.get_conflicting_reservations():
				suitable_tables.append(table)
			
			# Restore original table
			self.table = original_table
		
		return suitable_tables


@frappe.whitelist()
def get_available_time_slots(date, party_size, table=None):
	"""
	Get available time slots for a given date and party size
	
	Args:
		date (str): Reservation date
		party_size (int): Number of people
		table (str): Specific table (optional)
	
	Returns:
		list: Available time slots
	"""
	# Define restaurant operating hours (this could come from settings)
	opening_time = "10:00:00"
	closing_time = "22:00:00"
	slot_duration = 30  # minutes
	
	# Generate all possible time slots
	slots = []
	current_time = datetime.strptime(opening_time, "%H:%M:%S").time()
	closing = datetime.strptime(closing_time, "%H:%M:%S").time()
	
	while current_time < closing:
		slots.append(current_time.strftime("%H:%M:%S"))
		current_time = (datetime.combine(datetime.today(), current_time) + 
						timedelta(minutes=slot_duration)).time()
	
	# Filter out unavailable slots
	available_slots = []
	for slot in slots:
		if is_time_slot_available(date, slot, party_size, table):
			available_slots.append(slot)
	
	return available_slots


def is_time_slot_available(date, time, party_size, table=None):
	"""Check if a specific time slot is available"""
	# Get suitable tables if no specific table is requested
	if not table:
		from restaurant_management.restaurant_operations.doctype.restaurant_table.restaurant_table import get_available_tables
		suitable_tables = get_available_tables(min_capacity=party_size)
		if not suitable_tables:
			return False
		table_names = [t["name"] for t in suitable_tables]
	else:
		table_names = [table]
	
	# Check for conflicting reservations
	time_buffer = timedelta(hours=2)
	slot_datetime = datetime.combine(getdate(date), get_time(time))
	start_time = slot_datetime - time_buffer
	end_time = slot_datetime + time_buffer
	
	conflicting = frappe.db.sql("""
		SELECT COUNT(*) as count
		FROM `tabTable Reservation`
		WHERE table IN %(tables)s
		AND status IN ('Confirmed', 'Seated')
		AND TIMESTAMP(reservation_date, reservation_time) BETWEEN %(start_time)s AND %(end_time)s
	""", {
		"tables": table_names,
		"start_time": start_time,
		"end_time": end_time
	})[0][0]
	
	return conflicting == 0


@frappe.whitelist()
def get_reservations_for_date(date):
	"""Get all reservations for a specific date"""
	reservations = frappe.get_all(
		"Table Reservation",
		filters={"reservation_date": date},
		fields=[
			"name", "customer_name", "phone_number", "reservation_time",
			"party_size", "table", "status", "special_requests"
		],
		order_by="reservation_time"
	)
	
	return reservations
