{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2024-01-01 00:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "customer_name", "phone_number", "email", "column_break_5", "reservation_date", "reservation_time", "party_size", "section_break_9", "table", "status", "column_break_12", "special_requests", "notes", "section_break_15", "created_by", "created_on", "column_break_18", "confirmed_by", "confirmed_on"], "fields": [{"default": "RES-.YYYY.-.MM.-.#####", "fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "RES-.YYYY.-.MM.-.#####", "reqd": 1}, {"fieldname": "customer_name", "fieldtype": "Data", "in_list_view": 1, "label": "Customer Name", "reqd": 1}, {"fieldname": "phone_number", "fieldtype": "Data", "in_list_view": 1, "label": "Phone Number", "reqd": 1}, {"fieldname": "email", "fieldtype": "Data", "label": "Email"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "reservation_date", "fieldtype": "Date", "in_list_view": 1, "label": "Reservation Date", "reqd": 1}, {"fieldname": "reservation_time", "fieldtype": "Time", "in_list_view": 1, "label": "Reservation Time", "reqd": 1}, {"fieldname": "party_size", "fieldtype": "Int", "in_list_view": 1, "label": "Party Size", "reqd": 1}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "label": "Reservation Details"}, {"fieldname": "table", "fieldtype": "Link", "label": "Table", "options": "Restaurant Table"}, {"default": "Pending", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Pending\nConfirmed\nSeated\nNo Show\nCancelled", "reqd": 1}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"fieldname": "special_requests", "fieldtype": "Text", "label": "Special Requests"}, {"fieldname": "notes", "fieldtype": "Text", "label": "Notes"}, {"fieldname": "section_break_15", "fieldtype": "Section Break", "label": "Tracking Information"}, {"default": "user", "fieldname": "created_by", "fieldtype": "Link", "label": "Created By", "options": "User", "read_only": 1}, {"default": "now", "fieldname": "created_on", "fieldtype": "Datetime", "label": "Created On", "read_only": 1}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"fieldname": "confirmed_by", "fieldtype": "Link", "label": "Confirmed By", "options": "User", "read_only": 1}, {"fieldname": "confirmed_on", "fieldtype": "Datetime", "label": "Confirmed On", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "Restaurant Operations", "name": "Table Reservation", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Restaurant Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Restaurant Staff", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}