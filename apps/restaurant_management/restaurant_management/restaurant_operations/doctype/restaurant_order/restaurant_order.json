{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2024-01-01 00:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "order_date", "table", "column_break_4", "customer_name", "phone_number", "status", "section_break_8", "items", "section_break_10", "total_amount", "tax_amount", "grand_total", "column_break_14", "payment_method", "payment_status", "section_break_17", "special_requests", "notes"], "fields": [{"default": "ORD-.YYYY.-.MM.-.#####", "fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "ORD-.YYYY.-.MM.-.#####", "reqd": 1}, {"default": "Today", "fieldname": "order_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Order Date", "reqd": 1}, {"fieldname": "table", "fieldtype": "Link", "in_list_view": 1, "label": "Table", "options": "Restaurant Table"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "customer_name", "fieldtype": "Data", "in_list_view": 1, "label": "Customer Name"}, {"fieldname": "phone_number", "fieldtype": "Data", "label": "Phone Number"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Draft\nConfirmed\nPreparing\nReady\nServed\nPaid\nCancelled", "reqd": 1}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Order Items"}, {"fieldname": "items", "fieldtype": "Table", "label": "Items", "options": "Restaurant Order Item", "reqd": 1}, {"fieldname": "section_break_10", "fieldtype": "Section Break", "label": "Totals"}, {"fieldname": "total_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Amount", "read_only": 1}, {"fieldname": "tax_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Tax Amount"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Grand Total", "read_only": 1}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "payment_method", "fieldtype": "Select", "label": "Payment Method", "options": "Cash\nCard\nDigital Wallet\nBank Transfer"}, {"default": "Pending", "fieldname": "payment_status", "fieldtype": "Select", "label": "Payment Status", "options": "Pending\nPaid\nPartially Paid\nRefunded"}, {"fieldname": "section_break_17", "fieldtype": "Section Break", "label": "Additional Information"}, {"fieldname": "special_requests", "fieldtype": "Text", "label": "Special Requests"}, {"fieldname": "notes", "fieldtype": "Text", "label": "Notes"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "Restaurant Operations", "name": "Restaurant Order", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Restaurant Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Restaurant Staff", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}