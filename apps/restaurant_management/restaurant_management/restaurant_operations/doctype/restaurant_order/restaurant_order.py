# Copyright (c) 2024, anik and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now_datetime, flt


class RestaurantOrder(Document):
	"""
	Restaurant Order DocType Controller
	
	This class handles the complete order lifecycle including:
	- Order validation and calculations
	- Table management integration
	- Status workflow management
	- Payment processing
	"""
	
	def validate(self):
		"""Validate order data before saving"""
		self.validate_items()
		self.calculate_totals()
		self.validate_table_availability()
	
	def validate_items(self):
		"""Validate order items"""
		if not self.items:
			frappe.throw("Order must have at least one item")
		
		for item in self.items:
			# Check if menu item is available
			menu_item = frappe.get_doc("Menu Item", item.menu_item)
			if not menu_item.is_available:
				frappe.throw(f"Menu item '{menu_item.item_name}' is not available")
	
	def calculate_totals(self):
		"""Calculate order totals"""
		self.total_amount = 0
		
		for item in self.items:
			if item.quantity and item.rate:
				item.amount = flt(item.quantity) * flt(item.rate)
				self.total_amount += item.amount
		
		# Calculate grand total including tax
		if not self.tax_amount:
			self.tax_amount = 0
		
		self.grand_total = flt(self.total_amount) + flt(self.tax_amount)
	
	def validate_table_availability(self):
		"""Check if table is available for new orders"""
		if self.table and self.is_new():
			table_doc = frappe.get_doc("Restaurant Table", self.table)
			if table_doc.status == "Occupied" and table_doc.current_order != self.name:
				frappe.throw(f"Table {self.table} is already occupied")
	
	def before_save(self):
		"""Actions before saving the document"""
		# Set order date if not provided
		if not self.order_date:
			self.order_date = now_datetime()
	
	def on_submit(self):
		"""Actions when order is submitted"""
		self.status = "Confirmed"
		self.update_table_status()
		self.send_to_kitchen()
	
	def on_cancel(self):
		"""Actions when order is cancelled"""
		self.status = "Cancelled"
		self.free_table()
	
	def update_table_status(self):
		"""Update table status when order is confirmed"""
		if self.table:
			table_doc = frappe.get_doc("Restaurant Table", self.table)
			table_doc.set_occupied(self.name)
	
	def free_table(self):
		"""Free the table when order is completed or cancelled"""
		if self.table:
			table_doc = frappe.get_doc("Restaurant Table", self.table)
			table_doc.set_available()
	
	def send_to_kitchen(self):
		"""Send order to kitchen (placeholder for kitchen integration)"""
		# This could integrate with a kitchen display system
		frappe.msgprint(f"Order {self.name} sent to kitchen", alert=True)
	
	@frappe.whitelist()
	def mark_as_preparing(self):
		"""Mark order as preparing"""
		self.status = "Preparing"
		self.save()
		return self.status
	
	@frappe.whitelist()
	def mark_as_ready(self):
		"""Mark order as ready for serving"""
		self.status = "Ready"
		self.save()
		return self.status
	
	@frappe.whitelist()
	def mark_as_served(self):
		"""Mark order as served"""
		self.status = "Served"
		self.save()
		return self.status
	
	@frappe.whitelist()
	def mark_as_paid(self, payment_method=None):
		"""Mark order as paid"""
		self.status = "Paid"
		self.payment_status = "Paid"
		if payment_method:
			self.payment_method = payment_method
		self.save()
		
		# Free the table when payment is complete
		self.free_table()
		
		return self.status
	
	@frappe.whitelist()
	def add_item(self, menu_item, quantity=1, special_instructions=None):
		"""Add item to order"""
		menu_item_doc = frappe.get_doc("Menu Item", menu_item)
		
		# Check if item already exists in order
		existing_item = None
		for item in self.items:
			if item.menu_item == menu_item:
				existing_item = item
				break
		
		if existing_item:
			# Update quantity of existing item
			existing_item.quantity += quantity
			if special_instructions:
				existing_item.special_instructions = special_instructions
		else:
			# Add new item
			self.append("items", {
				"menu_item": menu_item,
				"item_name": menu_item_doc.item_name,
				"quantity": quantity,
				"rate": menu_item_doc.price,
				"special_instructions": special_instructions
			})
		
		self.calculate_totals()
		self.save()
		return self.items
	
	@frappe.whitelist()
	def remove_item(self, item_idx):
		"""Remove item from order"""
		if 0 <= item_idx < len(self.items):
			self.items.pop(item_idx)
			self.calculate_totals()
			self.save()
		return self.items
	
	@frappe.whitelist()
	def get_order_summary(self):
		"""Get comprehensive order summary"""
		return {
			"order_id": self.name,
			"table": self.table,
			"customer_name": self.customer_name,
			"status": self.status,
			"order_date": self.order_date,
			"total_items": len(self.items),
			"grand_total": self.grand_total,
			"payment_status": self.payment_status,
			"items": [
				{
					"item_name": item.item_name,
					"quantity": item.quantity,
					"rate": item.rate,
					"amount": item.amount,
					"status": item.status,
					"special_instructions": item.special_instructions
				}
				for item in self.items
			]
		}


@frappe.whitelist()
def create_quick_order(table, customer_name, items_data):
	"""
	Create a quick order with items
	
	Args:
		table (str): Table name
		customer_name (str): Customer name
		items_data (list): List of items with menu_item and quantity
	
	Returns:
		dict: Created order details
	"""
	order = frappe.get_doc({
		"doctype": "Restaurant Order",
		"table": table,
		"customer_name": customer_name,
		"order_date": now_datetime(),
		"status": "Draft"
	})
	
	# Add items
	for item_data in items_data:
		menu_item_doc = frappe.get_doc("Menu Item", item_data["menu_item"])
		order.append("items", {
			"menu_item": item_data["menu_item"],
			"item_name": menu_item_doc.item_name,
			"quantity": item_data.get("quantity", 1),
			"rate": menu_item_doc.price,
			"special_instructions": item_data.get("special_instructions")
		})
	
	order.insert()
	return order.get_order_summary()


@frappe.whitelist()
def get_active_orders(table=None, status=None):
	"""Get active orders with optional filters"""
	filters = {"status": ["not in", ["Paid", "Cancelled"]]}
	
	if table:
		filters["table"] = table
	
	if status:
		filters["status"] = status
	
	orders = frappe.get_all(
		"Restaurant Order",
		filters=filters,
		fields=[
			"name", "table", "customer_name", "status", 
			"order_date", "grand_total", "payment_status"
		],
		order_by="order_date desc"
	)
	
	return orders


@frappe.whitelist()
def get_order_analytics(from_date=None, to_date=None):
	"""Get order analytics for reporting"""
	date_filter = ""
	if from_date and to_date:
		date_filter = f"AND DATE(order_date) BETWEEN '{from_date}' AND '{to_date}'"
	
	analytics = frappe.db.sql(f"""
		SELECT 
			DATE(order_date) as order_date,
			COUNT(*) as total_orders,
			SUM(grand_total) as total_revenue,
			AVG(grand_total) as avg_order_value,
			COUNT(CASE WHEN status = 'Paid' THEN 1 END) as completed_orders
		FROM `tabRestaurant Order`
		WHERE docstatus != 2 {date_filter}
		GROUP BY DATE(order_date)
		ORDER BY order_date DESC
	""", as_dict=True)
	
	return analytics
