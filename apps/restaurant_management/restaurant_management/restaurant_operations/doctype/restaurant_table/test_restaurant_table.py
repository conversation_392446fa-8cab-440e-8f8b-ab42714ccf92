# Copyright (c) 2024, anik and Contributors
# See license.txt

import frappe
import unittest
from frappe.tests.utils import FrappeTestCase


class TestRestaurantTable(FrappeTestCase):
	"""Test cases for Restaurant Table DocType"""
	
	def setUp(self):
		"""Set up test data"""
		# Clean up any existing test data
		frappe.db.delete("Restaurant Table", {"table_name": ["like", "TEST%"]})
		frappe.db.commit()
	
	def tearDown(self):
		"""Clean up after tests"""
		frappe.db.delete("Restaurant Table", {"table_name": ["like", "TEST%"]})
		frappe.db.commit()
	
	def test_create_restaurant_table(self):
		"""Test creating a restaurant table"""
		table = frappe.get_doc({
			"doctype": "Restaurant Table",
			"table_name": "TEST-01",
			"capacity": 4,
			"location": "Indoor",
			"table_type": "Regular"
		})
		table.insert()
		
		# Verify the table was created
		self.assertEqual(table.table_name, "TEST-01")
		self.assertEqual(table.capacity, 4)
		self.assertEqual(table.status, "Available")  # Default status
	
	def test_table_name_validation(self):
		"""Test table name validation"""
		table = frappe.get_doc({
			"doctype": "Restaurant Table",
			"table_name": "test-02",  # lowercase
			"capacity": 2,
			"location": "Outdoor"
		})
		table.insert()
		
		# Should be converted to uppercase
		self.assertEqual(table.table_name, "TEST-02")
	
	def test_capacity_validation(self):
		"""Test capacity validation"""
		# Test zero capacity
		with self.assertRaises(frappe.ValidationError):
			table = frappe.get_doc({
				"doctype": "Restaurant Table",
				"table_name": "TEST-03",
				"capacity": 0,
				"location": "Indoor"
			})
			table.insert()
		
		# Test negative capacity
		with self.assertRaises(frappe.ValidationError):
			table = frappe.get_doc({
				"doctype": "Restaurant Table",
				"table_name": "TEST-04",
				"capacity": -1,
				"location": "Indoor"
			})
			table.insert()
	
	def test_status_changes(self):
		"""Test table status management"""
		table = frappe.get_doc({
			"doctype": "Restaurant Table",
			"table_name": "TEST-05",
			"capacity": 6,
			"location": "Indoor"
		})
		table.insert()
		
		# Test setting occupied
		table.set_occupied("ORDER-001")
		table.reload()
		self.assertEqual(table.status, "Occupied")
		self.assertEqual(table.current_order, "ORDER-001")
		
		# Test setting available
		table.set_available()
		table.reload()
		self.assertEqual(table.status, "Available")
		self.assertIsNone(table.current_order)
		
		# Test setting reserved
		table.set_reserved("Reserved for John Doe at 7 PM")
		table.reload()
		self.assertEqual(table.status, "Reserved")
		self.assertEqual(table.reservation_details, "Reserved for John Doe at 7 PM")
	
	def test_get_available_tables(self):
		"""Test getting available tables"""
		# Create test tables
		tables_data = [
			{"table_name": "TEST-06", "capacity": 2, "location": "Indoor", "status": "Available"},
			{"table_name": "TEST-07", "capacity": 4, "location": "Outdoor", "status": "Available"},
			{"table_name": "TEST-08", "capacity": 6, "location": "Indoor", "status": "Occupied"},
		]
		
		for data in tables_data:
			table = frappe.get_doc(dict(doctype="Restaurant Table", **data))
			table.insert()
		
		# Test getting all available tables
		from restaurant_management.restaurant_operations.doctype.restaurant_table.restaurant_table import get_available_tables
		available = get_available_tables()
		available_names = [t["table_name"] for t in available]
		
		self.assertIn("TEST-06", available_names)
		self.assertIn("TEST-07", available_names)
		self.assertNotIn("TEST-08", available_names)  # Occupied table should not be included
		
		# Test filtering by location
		indoor_tables = get_available_tables(location="Indoor")
		indoor_names = [t["table_name"] for t in indoor_tables]
		self.assertIn("TEST-06", indoor_names)
		self.assertNotIn("TEST-07", indoor_names)  # Outdoor table
		
		# Test filtering by capacity
		large_tables = get_available_tables(min_capacity=4)
		large_names = [t["table_name"] for t in large_tables]
		self.assertNotIn("TEST-06", large_names)  # Capacity 2
		self.assertIn("TEST-07", large_names)  # Capacity 4
