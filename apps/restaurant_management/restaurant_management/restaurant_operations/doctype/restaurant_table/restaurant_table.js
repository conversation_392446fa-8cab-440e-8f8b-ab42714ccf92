// Copyright (c) 2024, anik and contributors
// For license information, please see license.txt

frappe.ui.form.on('Restaurant Table', {
	refresh: function(frm) {
		// Add custom buttons based on table status
		if (!frm.doc.__islocal) {
			add_status_buttons(frm);
			add_info_section(frm);
		}
		
		// Set color indicator based on status
		set_status_indicator(frm);
	},
	
	capacity: function(frm) {
		// Validate capacity
		if (frm.doc.capacity > 12) {
			frappe.msgprint({
				title: __('Large Table'),
				message: __('This is a large table. Please ensure adequate space allocation.'),
				indicator: 'orange'
			});
		}
	},
	
	status: function(frm) {
		set_status_indicator(frm);
	}
});

function add_status_buttons(frm) {
	// Add quick action buttons based on current status
	if (frm.doc.status === 'Available') {
		frm.add_custom_button(__('Mark as Occupied'), function() {
			mark_table_occupied(frm);
		}, __('Actions'));
		
		frm.add_custom_button(__('Mark as Reserved'), function() {
			mark_table_reserved(frm);
		}, __('Actions'));
		
		frm.add_custom_button(__('Out of Service'), function() {
			mark_table_out_of_service(frm);
		}, __('Actions'));
	}
	
	if (frm.doc.status === 'Occupied') {
		frm.add_custom_button(__('Mark as Available'), function() {
			mark_table_available(frm);
		}, __('Actions'));
		
		if (frm.doc.current_order) {
			frm.add_custom_button(__('View Order'), function() {
				frappe.set_route('Form', 'Restaurant Order', frm.doc.current_order);
			}, __('Actions'));
		}
	}
	
	if (frm.doc.status === 'Reserved') {
		frm.add_custom_button(__('Mark as Available'), function() {
			mark_table_available(frm);
		}, __('Actions'));
		
		frm.add_custom_button(__('Mark as Occupied'), function() {
			mark_table_occupied(frm);
		}, __('Actions'));
	}
	
	if (frm.doc.status === 'Out of Service') {
		frm.add_custom_button(__('Mark as Available'), function() {
			mark_table_available(frm);
		}, __('Actions'));
	}
}

function add_info_section(frm) {
	// Add information section showing table statistics
	frappe.call({
		method: 'get_table_status_summary',
		doc: frm.doc,
		callback: function(r) {
			if (r.message) {
				let info_html = `
					<div class="table-info-section">
						<h5>Table Information</h5>
						<div class="row">
							<div class="col-md-6">
								<p><strong>Current Status:</strong> ${r.message.status}</p>
								<p><strong>Capacity:</strong> ${r.message.capacity} people</p>
								<p><strong>Location:</strong> ${r.message.location}</p>
							</div>
							<div class="col-md-6">
								<p><strong>Current Order:</strong> ${r.message.current_order || 'None'}</p>
								<p><strong>Last Updated:</strong> ${frappe.datetime.str_to_user(r.message.last_updated)}</p>
							</div>
						</div>
					</div>
				`;
				
				frm.dashboard.add_section(info_html, __('Table Status'));
			}
		}
	});
}

function set_status_indicator(frm) {
	// Set form indicator based on table status
	let indicator_map = {
		'Available': 'green',
		'Occupied': 'red',
		'Reserved': 'orange',
		'Out of Service': 'gray'
	};
	
	let color = indicator_map[frm.doc.status] || 'blue';
	frm.dashboard.set_headline_alert(
		`<div class="indicator ${color}">Table Status: ${frm.doc.status}</div>`
	);
}

function mark_table_occupied(frm) {
	frappe.prompt([
		{
			label: 'Order Number',
			fieldname: 'order_number',
			fieldtype: 'Link',
			options: 'Restaurant Order',
			reqd: 0
		}
	], function(values) {
		frappe.call({
			method: 'set_occupied',
			doc: frm.doc,
			args: {
				order_name: values.order_number
			},
			callback: function(r) {
				frm.reload_doc();
				frappe.show_alert({
					message: __('Table marked as occupied'),
					indicator: 'green'
				});
			}
		});
	}, __('Mark Table as Occupied'));
}

function mark_table_reserved(frm) {
	frappe.prompt([
		{
			label: 'Reservation Details',
			fieldname: 'reservation_details',
			fieldtype: 'Text',
			reqd: 1
		}
	], function(values) {
		frappe.call({
			method: 'set_reserved',
			doc: frm.doc,
			args: {
				reservation_details: values.reservation_details
			},
			callback: function(r) {
				frm.reload_doc();
				frappe.show_alert({
					message: __('Table marked as reserved'),
					indicator: 'orange'
				});
			}
		});
	}, __('Reserve Table'));
}

function mark_table_available(frm) {
	frappe.confirm(
		__('Are you sure you want to mark this table as available?'),
		function() {
			frappe.call({
				method: 'set_available',
				doc: frm.doc,
				callback: function(r) {
					frm.reload_doc();
					frappe.show_alert({
						message: __('Table marked as available'),
						indicator: 'green'
					});
				}
			});
		}
	);
}

function mark_table_out_of_service(frm) {
	frappe.prompt([
		{
			label: 'Reason',
			fieldname: 'reason',
			fieldtype: 'Text',
			reqd: 1
		}
	], function(values) {
		frm.set_value('status', 'Out of Service');
		frm.set_value('description', (frm.doc.description || '') + '\nOut of service: ' + values.reason);
		frm.save();
	}, __('Mark Out of Service'));
}
