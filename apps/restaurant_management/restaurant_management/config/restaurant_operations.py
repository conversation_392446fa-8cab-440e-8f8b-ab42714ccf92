"""
Restaurant Operations Module Configuration
This file defines the module structure and navigation
"""

from frappe import _


def get_data():
	"""Return module configuration"""
	return [
		{
			"label": _("Restaurant Operations"),
			"items": [
				{
					"type": "doctype",
					"name": "Restaurant Table",
					"label": _("Restaurant Table"),
					"description": _("Manage restaurant tables and seating")
				},
				{
					"type": "doctype", 
					"name": "Restaurant Order",
					"label": _("Restaurant Order"),
					"description": _("Create and manage customer orders")
				},
				{
					"type": "doctype",
					"name": "Table Reservation", 
					"label": _("Table Reservation"),
					"description": _("Manage table reservations and bookings")
				}
			]
		},
		{
			"label": _("Reports"),
			"items": [
				{
					"type": "report",
					"name": "Daily Sales Summary",
					"label": _("Daily Sales Summary"),
					"doctype": "Restaurant Order",
					"is_query_report": False
				}
			]
		},
		{
			"label": _("Analytics"),
			"items": [
				{
					"type": "dashboard",
					"name": "Restaurant Dashboard",
					"label": _("Restaurant Dashboard")
				}
			]
		},
		{
			"label": _("Setup"),
			"items": [
				{
					"type": "doctype",
					"name": "Restaurant Settings",
					"label": _("Restaurant Settings"),
					"description": _("Configure restaurant settings")
				}
			]
		}
	]
