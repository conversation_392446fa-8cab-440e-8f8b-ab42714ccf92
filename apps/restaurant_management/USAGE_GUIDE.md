# Restaurant Management System - Usage Guide

This guide will walk you through using the Restaurant Management System and understanding its key concepts.

## 🎯 Quick Start

### 1. Setting Up Tables

First, create your restaurant tables:

```python
# Create a table via Python console
table = frappe.get_doc({
    "doctype": "Restaurant Table",
    "table_name": "TABLE-01",
    "capacity": 4,
    "location": "Indoor",
    "table_type": "Regular",
    "description": "Window-side table for 4 people"
})
table.insert()
```

Or use the UI:
1. Go to **Restaurant Operations > Restaurant Table**
2. Click **New**
3. Fill in table details
4. Save

### 2. Creating Menu Items

Add items to your menu:

```python
# Create a menu item
burger = frappe.get_doc({
    "doctype": "Menu Item",
    "item_name": "Classic Burger",
    "category": "Main Course",
    "price": 12.99,
    "cost_price": 6.50,
    "preparation_time": 15,
    "description": "Juicy beef patty with lettuce, tomato, and special sauce",
    "is_vegetarian": 0,
    "is_spicy": 0,
    "calories": 650
})
burger.insert()
```

### 3. Taking Orders

Create customer orders:

```python
# Create an order
order = frappe.get_doc({
    "doctype": "Restaurant Order",
    "table": "TABLE-01",
    "customer_name": "<PERSON> Doe",
    "phone_number": "+1234567890"
})

# Add items to the order
order.append("items", {
    "menu_item": "Classic Burger",
    "quantity": 2,
    "special_instructions": "No onions"
})

order.append("items", {
    "menu_item": "French Fries",
    "quantity": 2
})

order.insert()
order.submit()  # This confirms the order
```

### 4. Managing Reservations

Handle table reservations:

```python
# Create a reservation
reservation = frappe.get_doc({
    "doctype": "Table Reservation",
    "customer_name": "Jane Smith",
    "phone_number": "+1987654321",
    "reservation_date": "2024-01-15",
    "reservation_time": "19:00:00",
    "party_size": 4,
    "special_requests": "Birthday celebration - need cake service"
})
reservation.insert()

# Confirm the reservation
reservation.confirm_reservation()
```

## 🔄 Workflow Examples

### Order Processing Workflow

```python
# 1. Create draft order
order = frappe.get_doc("Restaurant Order", "ORD-2024-01-00001")

# 2. Confirm order (sends to kitchen)
order.submit()

# 3. Mark as preparing
order.mark_as_preparing()

# 4. Mark as ready
order.mark_as_ready()

# 5. Mark as served
order.mark_as_served()

# 6. Process payment
order.mark_as_paid("Card")
```

### Table Status Management

```python
# Get a table
table = frappe.get_doc("Restaurant Table", "TABLE-01")

# Check current status
print(f"Table status: {table.status}")

# Mark as occupied
table.set_occupied("ORD-2024-01-00001")

# Mark as available when done
table.set_available()

# Mark as reserved
table.set_reserved("Reserved for John Doe at 7 PM")

# Mark out of service
table.status = "Out of Service"
table.description = "Cleaning in progress"
table.save()
```

## 📊 Using Reports

### Daily Sales Summary

Access via: **Restaurant Operations > Reports > Daily Sales Summary**

This report shows:
- Total orders and revenue by date
- Payment method breakdown
- Order completion rates
- Revenue trends with charts

### Menu Performance

Access via: **Menu Management > Reports > Menu Performance**

This report shows:
- Best-selling items
- Profit margins by item
- Category performance
- Items that need attention

## 🧪 Testing Your Setup

### Create Sample Data

```python
# Run this in the Python console to create sample data

# Create tables
tables = [
    {"table_name": "TABLE-01", "capacity": 2, "location": "Indoor"},
    {"table_name": "TABLE-02", "capacity": 4, "location": "Indoor"},
    {"table_name": "TABLE-03", "capacity": 6, "location": "Outdoor"},
    {"table_name": "TABLE-04", "capacity": 8, "location": "Private Room"}
]

for table_data in tables:
    table = frappe.get_doc(dict(doctype="Restaurant Table", **table_data))
    table.insert()

# Create menu items
menu_items = [
    {
        "item_name": "Classic Burger",
        "category": "Main Course",
        "price": 12.99,
        "cost_price": 6.50,
        "preparation_time": 15
    },
    {
        "item_name": "Caesar Salad",
        "category": "Salad",
        "price": 8.99,
        "cost_price": 3.50,
        "preparation_time": 8,
        "is_vegetarian": 1
    },
    {
        "item_name": "Chocolate Cake",
        "category": "Dessert",
        "price": 6.99,
        "cost_price": 2.50,
        "preparation_time": 5
    },
    {
        "item_name": "Coffee",
        "category": "Beverage",
        "price": 3.99,
        "cost_price": 1.00,
        "preparation_time": 3
    }
]

for item_data in menu_items:
    item = frappe.get_doc(dict(doctype="Menu Item", **item_data))
    item.insert()

print("Sample data created successfully!")
```

### Test Order Flow

```python
# Create a complete order flow
def test_order_flow():
    # Create order
    order = frappe.get_doc({
        "doctype": "Restaurant Order",
        "table": "TABLE-01",
        "customer_name": "Test Customer",
        "items": [
            {
                "menu_item": "Classic Burger",
                "quantity": 1
            },
            {
                "menu_item": "Coffee",
                "quantity": 1
            }
        ]
    })
    order.insert()
    
    # Process through workflow
    order.submit()
    order.mark_as_preparing()
    order.mark_as_ready()
    order.mark_as_served()
    order.mark_as_paid("Cash")
    
    print(f"Order {order.name} completed successfully!")
    return order

# Run the test
test_order = test_order_flow()
```

## 🎨 Customization Examples

### Adding Custom Fields

```python
# Add a custom field to Restaurant Table
custom_field = frappe.get_doc({
    "doctype": "Custom Field",
    "dt": "Restaurant Table",
    "fieldname": "wifi_password",
    "label": "WiFi Password",
    "fieldtype": "Data",
    "insert_after": "description"
})
custom_field.insert()
```

### Custom Validation

```python
# Add custom validation in restaurant_table.py
def validate(self):
    # Existing validations...
    
    # Custom validation: VIP tables must have capacity >= 4
    if self.table_type == "VIP" and self.capacity < 4:
        frappe.throw("VIP tables must have capacity of at least 4")
```

### Custom Client Script

```javascript
// Add to restaurant_order.js
frappe.ui.form.on('Restaurant Order', {
    customer_name: function(frm) {
        // Auto-suggest phone number based on customer history
        if (frm.doc.customer_name) {
            frappe.call({
                method: 'get_customer_phone',
                args: {
                    customer_name: frm.doc.customer_name
                },
                callback: function(r) {
                    if (r.message) {
                        frm.set_value('phone_number', r.message);
                    }
                }
            });
        }
    }
});
```

## 🔍 Troubleshooting

### Common Issues

1. **Table not updating status**
   - Check if the table exists
   - Ensure proper permissions
   - Verify the status value is valid

2. **Order totals not calculating**
   - Check if menu items have prices set
   - Verify quantity is a positive number
   - Ensure the calculate_totals method is called

3. **Reports showing no data**
   - Verify you have orders with status "Paid"
   - Check date filters
   - Ensure proper permissions for the report

### Debug Commands

```python
# Check table status
table = frappe.get_doc("Restaurant Table", "TABLE-01")
print(f"Status: {table.status}, Current Order: {table.current_order}")

# Check order items
order = frappe.get_doc("Restaurant Order", "ORD-2024-01-00001")
for item in order.items:
    print(f"Item: {item.item_name}, Qty: {item.quantity}, Amount: {item.amount}")

# Check menu item availability
items = frappe.get_all("Menu Item", filters={"is_available": 1}, fields=["item_name", "price"])
print("Available items:", items)
```

## 📚 Next Steps

1. **Explore the Code**: Look at the Python controllers to understand business logic
2. **Customize Forms**: Add fields and modify layouts using Form Customization
3. **Create Custom Reports**: Build reports specific to your needs
4. **Add Integrations**: Connect with POS systems, payment gateways, etc.
5. **Mobile Interface**: Create mobile-friendly views for staff
6. **API Integration**: Build external integrations using Frappe's REST API

## 🤝 Getting Help

- Check the ERPNext documentation
- Review the source code in this app
- Use the Frappe/ERPNext community forums
- Debug using the browser developer tools and server logs
