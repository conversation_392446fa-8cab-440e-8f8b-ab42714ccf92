2025-09-17 22:27:55,364 Worker rq:worker:******************************** started with PID 18188, version 1.15.1
2025-09-17 22:27:55,364 Subscribing to channel rq:pubsub:********************************
2025-09-17 22:27:55,366 *** Listening on home-anik-frappe-bench:short, home-anik-frappe-bench:default, home-anik-frappe-bench:long...
2025-09-17 22:27:55,367 Cleaning registries for queue: home-anik-frappe-bench:short
2025-09-17 22:27:55,370 Cleaning registries for queue: home-anik-frappe-bench:default
2025-09-17 22:27:55,372 Cleaning registries for queue: home-anik-frappe-bench:long
2025-09-17 22:27:55,382 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.create_contact', kwargs={'user': <User: <EMAIL>>, 'ignore_mandatory': True}, method='frappe.core.doctype.user.user.create_contact', site='restaurant.local', user='Administrator') (restaurant.local::53e6719c-b912-43d9-a13d-bdef094a1c03)
2025-09-17 22:27:56,814 home-anik-frappe-bench:default: Job OK (restaurant.local::53e6719c-b912-43d9-a13d-bdef094a1c03)
2025-09-17 22:27:56,814 Result is kept for 600 seconds
2025-09-17 22:27:56,833 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.update_gravatar', kwargs={'name': '<EMAIL>'}, method='frappe.core.doctype.user.user.update_gravatar', site='restaurant.local', user='Administrator') (restaurant.local::4522f731-99d0-4564-9177-5b752ecacd68)
2025-09-17 22:27:57,892 home-anik-frappe-bench:default: Job OK (restaurant.local::4522f731-99d0-4564-9177-5b752ecacd68)
2025-09-17 22:27:57,892 Result is kept for 600 seconds
2025-09-17 22:27:57,903 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.create_contact', kwargs={'user': <User: <EMAIL>>, 'ignore_mandatory': True}, method='frappe.core.doctype.user.user.create_contact', site='restaurant.local', user='Administrator') (restaurant.local::4e42b387-d0b9-4fb4-a142-07406ac8b2df)
2025-09-17 22:27:59,088 home-anik-frappe-bench:default: Job OK (restaurant.local::4e42b387-d0b9-4fb4-a142-07406ac8b2df)
2025-09-17 22:27:59,088 Result is kept for 600 seconds
2025-09-17 22:27:59,099 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.user.user.update_gravatar', kwargs={'name': '<EMAIL>'}, method='frappe.core.doctype.user.user.update_gravatar', site='restaurant.local', user='Administrator') (restaurant.local::6fcb6a5c-844a-4c17-9407-6e03d6fd0b46)
2025-09-17 22:27:59,842 home-anik-frappe-bench:default: Job OK (restaurant.local::6fcb6a5c-844a-4c17-9407-6e03d6fd0b46)
2025-09-17 22:27:59,842 Result is kept for 600 seconds
2025-09-17 22:28:56,545 home-anik-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.core.doctype.prepared_report.prepared_report.expire_st..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report)
2025-09-17 22:28:57,132 home-anik-frappe-bench:long: Job OK (restaurant.local::scheduled_job::frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report)
2025-09-17 22:28:57,133 Result is kept for 600 seconds
2025-09-17 22:28:57,147 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.utils.global_search.sync_global_search'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-09-17 22:28:57,556 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.utils.global_search.sync_global_search)
2025-09-17 22:28:57,556 Result is kept for 600 seconds
2025-09-17 22:28:57,568 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.retry_sending_emails'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.email.queue.retry_sending_emails)
2025-09-17 22:28:58,034 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.email.queue.retry_sending_emails)
2025-09-17 22:28:58,034 Result is kept for 600 seconds
2025-09-17 22:28:58,047 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.link_count.update_link_count'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-09-17 22:28:58,500 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.model.utils.link_count.update_link_count)
2025-09-17 22:28:58,500 Result is kept for 600 seconds
2025-09-17 22:28:58,509 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.automation.doctype.reminder.reminder.send_reminders'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-09-17 22:28:58,979 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.automation.doctype.reminder.reminder.send_reminders)
2025-09-17 22:28:58,979 Result is kept for 600 seconds
2025-09-17 22:28:58,996 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.pull'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-09-17 22:28:59,537 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.email.doctype.email_account.email_account.pull)
2025-09-17 22:28:59,537 Result is kept for 600 seconds
2025-09-17 22:28:59,548 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.queue.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.email.queue.flush)
2025-09-17 22:29:00,193 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.email.queue.flush)
2025-09-17 22:29:00,194 Result is kept for 600 seconds
2025-09-17 22:29:00,203 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.integrations.doctype.google_calendar.google_calendar.s..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-09-17 22:29:00,723 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.integrations.doctype.google_calendar.google_calendar.sync)
2025-09-17 22:29:00,723 Result is kept for 600 seconds
2025-09-17 22:29:00,731 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.deferred_insert.save_to_db'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.deferred_insert.save_to_db)
2025-09-17 22:29:01,433 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.deferred_insert.save_to_db)
2025-09-17 22:29:01,433 Result is kept for 600 seconds
2025-09-17 22:29:01,446 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.newsletter.newsletter.send_scheduled_ema..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.email.doctype.newsletter.newsletter.send_scheduled_email)
2025-09-17 22:29:01,978 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.email.doctype.newsletter.newsletter.send_scheduled_email)
2025-09-17 22:29:01,978 Result is kept for 600 seconds
2025-09-17 22:29:01,987 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.monitor.flush'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.monitor.flush)
2025-09-17 22:29:02,386 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.monitor.flush)
2025-09-17 22:29:02,386 Result is kept for 600 seconds
2025-09-17 22:29:02,394 home-anik-frappe-bench:default: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.email.doctype.email_account.email_account.notify_unrep..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-09-17 22:29:02,889 home-anik-frappe-bench:default: Job OK (restaurant.local::scheduled_job::frappe.email.doctype.email_account.email_account.notify_unreplied)
2025-09-17 22:29:02,889 Result is kept for 600 seconds
2025-09-17 22:29:02,900 home-anik-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.form.document_follow.send_hourly_updates'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.desk.form.document_follow.send_hourly_updates)
2025-09-17 22:29:03,287 home-anik-frappe-bench:long: Job OK (restaurant.local::scheduled_job::frappe.desk.form.document_follow.send_hourly_updates)
2025-09-17 22:29:03,287 Result is kept for 600 seconds
2025-09-17 22:29:03,296 home-anik-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.page.backups.backups.delete_downloadable_backups'..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.desk.page.backups.backups.delete_downloadable_backups)
2025-09-17 22:29:03,970 home-anik-frappe-bench:long: Job OK (restaurant.local::scheduled_job::frappe.desk.page.backups.backups.delete_downloadable_backups)
2025-09-17 22:29:03,971 Result is kept for 600 seconds
2025-09-17 22:29:03,980 home-anik-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.twofactor.delete_all_barcodes_for_users'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.twofactor.delete_all_barcodes_for_users)
2025-09-17 22:29:04,709 home-anik-frappe-bench:long: Job OK (restaurant.local::scheduled_job::frappe.twofactor.delete_all_barcodes_for_users)
2025-09-17 22:29:04,709 Result is kept for 600 seconds
2025-09-17 22:29:04,727 home-anik-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.website.doctype.web_page.web_page.check_publish_status..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.website.doctype.web_page.web_page.check_publish_status)
2025-09-17 22:29:05,572 home-anik-frappe-bench:long: Job OK (restaurant.local::scheduled_job::frappe.website.doctype.web_page.web_page.check_publish_status)
2025-09-17 22:29:05,572 Result is kept for 600 seconds
2025-09-17 22:29:05,585 home-anik-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.oauth.delete_oauth2_data'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.oauth.delete_oauth2_data)
2025-09-17 22:29:06,355 home-anik-frappe-bench:long: Job OK (restaurant.local::scheduled_job::frappe.oauth.delete_oauth2_data)
2025-09-17 22:29:06,355 Result is kept for 600 seconds
2025-09-17 22:29:06,366 home-anik-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.model.utils.user_settings.sync_user_settings'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.model.utils.user_settings.sync_user_settings)
2025-09-17 22:29:06,971 home-anik-frappe-bench:long: Job OK (restaurant.local::scheduled_job::frappe.model.utils.user_settings.sync_user_settings)
2025-09-17 22:29:06,971 Result is kept for 600 seconds
2025-09-17 22:29:06,979 home-anik-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.website.doctype.personal_data_deletion_request.persona..., method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request)
2025-09-17 22:29:07,385 home-anik-frappe-bench:long: Job OK (restaurant.local::scheduled_job::frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request)
2025-09-17 22:29:07,385 Result is kept for 600 seconds
2025-09-17 22:29:07,392 home-anik-frappe-bench:long: frappe.utils.background_jobs.execute_job(event=None, is_async=True, job_name='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., kwargs={'job_type': 'frappe.desk.utils.delete_old_exported_report_files'}, method='frappe.core.doctype.scheduled_job_type.scheduled_job_type.run_scheduled_jo..., site='restaurant.local', user='Administrator') (restaurant.local::scheduled_job::frappe.desk.utils.delete_old_exported_report_files)
2025-09-17 22:29:07,764 home-anik-frappe-bench:long: Job OK (restaurant.local::scheduled_job::frappe.desk.utils.delete_old_exported_report_files)
2025-09-17 22:29:07,764 Result is kept for 600 seconds
2025-09-17 22:29:12,278 Worker ******************************** [PID 18188]: warm shut down requested
2025-09-17 22:29:12,280 Unsubscribing from channel rq:pubsub:********************************
