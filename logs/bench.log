2025-09-17 20:29:43,621 DEBUG cd frappe-bench && python3 -m venv env
2025-09-17 20:29:45,944 DEBUG cd frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-09-17 20:29:47,283 DEBUG cd frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet wheel
2025-09-17 20:29:49,495 LOG Getting frappe
2025-09-17 20:29:49,495 DEBUG cd frappe-bench/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-09-17 20:30:22,877 LOG Installing frappe
2025-09-17 20:30:22,878 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappe 
2025-09-17 20:33:57,335 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files
2025-09-17 20:35:05,823 DEBUG cd frappe-bench && bench build
2025-09-17 20:35:05,941 INFO /home/<USER>/.local/bin/bench build
2025-09-17 20:35:19,228 LOG setting up backups
2025-09-17 20:35:19,240 LOG backups were set up
2025-09-17 20:35:19,240 INFO Bench frappe-bench initialized
2025-09-17 20:51:49,621 INFO /home/<USER>/.local/bin/bench start
2025-09-17 20:51:49,626 WARNING /home/<USER>/.local/bin/bench start executed with exit code 1
2025-09-17 20:52:36,339 INFO /home/<USER>/.local/bin/bench start
2025-09-17 20:52:36,342 WARNING /home/<USER>/.local/bin/bench start executed with exit code 1
2025-09-17 21:30:27,856 INFO /home/<USER>/.local/bin/bench new-app restaurant_management
2025-09-17 21:30:27,872 LOG creating new app restaurant_management
2025-09-17 21:31:26,797 LOG Installing restaurant_management
2025-09-17 21:31:26,798 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/restaurant_management 
2025-09-17 21:31:29,081 DEBUG bench build --app restaurant_management
2025-09-17 21:31:29,241 INFO /home/<USER>/.local/bin/bench build --app restaurant_management
2025-09-17 21:31:40,310 INFO /home/<USER>/.local/bin/bench --site all install-app restaurant_management
2025-09-17 21:31:47,323 INFO /home/<USER>/.local/bin/bench --site all list-apps
2025-09-17 21:31:59,978 INFO /home/<USER>/.local/bin/bench new-site restaurant.local
2025-09-17 21:32:27,092 INFO /home/<USER>/.local/bin/bench make-doctype Restaurant Table
2025-09-17 21:32:34,595 INFO /home/<USER>/.local/bin/bench --site restaurant.local make-doctype Restaurant Table
2025-09-17 21:32:40,805 INFO /home/<USER>/.local/bin/bench --help
2025-09-17 21:51:23,571 INFO /home/<USER>/.local/bin/bench start
2025-09-17 21:51:23,577 WARNING /home/<USER>/.local/bin/bench start executed with exit code 1
2025-09-17 21:51:46,693 INFO /home/<USER>/.local/bin/bench new-site restaurant.local --admin-password admin
2025-09-17 21:51:59,095 INFO /home/<USER>/.local/bin/bench --site restaurant.local install-app restaurant_management
2025-09-17 21:52:25,046 INFO /home/<USER>/.local/bin/bench --site all list-apps
2025-09-17 21:52:37,646 INFO /home/<USER>/.local/bin/bench start
2025-09-17 21:52:37,658 WARNING /home/<USER>/.local/bin/bench start executed with exit code 1
2025-09-17 21:52:53,865 INFO /home/<USER>/.local/bin/bench drop-site restaurant.local --force
2025-09-17 21:54:41,552 INFO /home/<USER>/.local/bin/bench drop-site restaurant.local --mariadb-root-password 123 --force
2025-09-17 21:54:50,227 INFO /home/<USER>/.local/bin/bench new-site restaurant.local --mariadb-root-password 123 --admin-password admin
2025-09-17 21:55:41,007 INFO /home/<USER>/.local/bin/bench --site restaurant.local install-app restaurant_management
2025-09-17 21:56:25,957 INFO /home/<USER>/.local/bin/bench --site restaurant.local install-app restaurant_management
2025-09-17 21:56:35,984 INFO /home/<USER>/.local/bin/bench --site restaurant.local install-app restaurant_management --force
2025-09-17 22:00:35,251 INFO /home/<USER>/.local/bin/bench --site restaurant.local install-app restaurant_management --force
2025-09-17 22:00:48,056 INFO /home/<USER>/.local/bin/bench --site restaurant.local serve --port 8000
2025-09-17 22:01:24,561 INFO /home/<USER>/.local/bin/bench --site restaurant.local list-apps
2025-09-17 22:03:41,834 INFO /home/<USER>/.local/bin/bench config set-common-config -c redis_cache redis://127.0.0.1:6379
2025-09-17 22:03:41,846 WARNING /home/<USER>/.local/bin/bench config set-common-config -c redis_cache redis://127.0.0.1:6379 executed with exit code 1
2025-09-17 22:04:14,176 INFO /home/<USER>/.local/bin/bench --site restaurant.local serve --port 8000
