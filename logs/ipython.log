2025-09-17 22:10:05,493 INFO ipython === bench console session ===
2025-09-17 22:10:05,494 INFO ipython # Check if modules are properly installed
2025-09-17 22:10:05,494 INFO ipython print("Installed modules:")
2025-09-17 22:10:05,494 INFO ipython modules = frappe.get_all("Module Def", fields=["name", "app_name"])
2025-09-17 22:10:05,494 INFO ipython for module in modules:
        if module.app_name == "restaurant_management":
                    print(f"✓ {module.name}")
                    
2025-09-17 22:10:05,494 INFO ipython # Check if DocTypes are available
2025-09-17 22:10:05,494 INFO ipython print("\nRestaurant Management DocTypes:")
2025-09-17 22:10:05,494 INFO ipython doctypes = frappe.get_all("DocType", filters={"module": ["in", ["Restaurant Operations", "Menu Management"]]}, fields=["name", "module"])
2025-09-17 22:10:05,494 INFO ipython for dt in doctypes:
        print(f"✓ {dt.name} ({dt.module})")
        
2025-09-17 22:10:05,494 INFO ipython === session end ===
