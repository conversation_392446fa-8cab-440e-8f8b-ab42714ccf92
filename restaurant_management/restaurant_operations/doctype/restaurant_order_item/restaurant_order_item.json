{"actions": [], "creation": "2024-01-01 00:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["menu_item", "item_name", "quantity", "rate", "amount", "column_break_6", "special_instructions", "status"], "fields": [{"fieldname": "menu_item", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "reqd": 1}, {"fetch_from": "menu_item.item_name", "fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Name", "read_only": 1}, {"default": "1", "fieldname": "quantity", "fieldtype": "Int", "in_list_view": 1, "label": "Quantity", "reqd": 1}, {"fetch_from": "menu_item.price", "fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "read_only": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "read_only": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "special_instructions", "fieldtype": "Small Text", "label": "Special Instructions"}, {"default": "Ordered", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Ordered\nPreparing\nReady\nServed"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "Restaurant Operations", "name": "Restaurant Order Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}