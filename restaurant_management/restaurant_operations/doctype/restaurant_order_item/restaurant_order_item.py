# Copyright (c) 2024, anik and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class RestaurantOrderItem(Document):
	"""Child DocType for Restaurant Order Items"""
	
	def validate(self):
		"""Validate order item data"""
		self.validate_quantity()
		self.calculate_amount()
	
	def validate_quantity(self):
		"""Ensure quantity is valid"""
		if self.quantity <= 0:
			frappe.throw("Quantity must be greater than 0")
	
	def calculate_amount(self):
		"""Calculate amount based on quantity and rate"""
		if self.quantity and self.rate:
			self.amount = self.quantity * self.rate
