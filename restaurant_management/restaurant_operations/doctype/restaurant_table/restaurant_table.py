# Copyright (c) 2024, anik and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class RestaurantTable(Document):
	"""
	Restaurant Table DocType Controller
	
	This class handles the business logic for restaurant tables including:
	- Validation of table data
	- Status management
	- Integration with orders and reservations
	"""
	
	def validate(self):
		"""Validate the restaurant table data before saving"""
		self.validate_capacity()
		self.validate_table_name()
	
	def validate_capacity(self):
		"""Ensure capacity is a positive number"""
		if self.capacity <= 0:
			frappe.throw("Table capacity must be greater than 0")
		
		if self.capacity > 20:
			frappe.msgprint("Large table capacity detected. Please verify.", alert=True)
	
	def validate_table_name(self):
		"""Ensure table name follows naming convention"""
		if not self.table_name:
			frappe.throw("Table name is required")
		
		# Convert to uppercase for consistency
		self.table_name = self.table_name.upper()
	
	def before_save(self):
		"""Actions to perform before saving the document"""
		# Set default status if not provided
		if not self.status:
			self.status = "Available"
	
	def on_update(self):
		"""Actions to perform after updating the document"""
		# Log status changes for audit trail
		if self.has_value_changed("status"):
			self.add_comment("Info", f"Table status changed to {self.status}")
	
	def set_occupied(self, order_name=None):
		"""Mark table as occupied"""
		self.status = "Occupied"
		if order_name:
			self.current_order = order_name
		self.save()
		frappe.db.commit()
	
	def set_available(self):
		"""Mark table as available"""
		self.status = "Available"
		self.current_order = None
		self.reservation_details = None
		self.save()
		frappe.db.commit()
	
	def set_reserved(self, reservation_details=None):
		"""Mark table as reserved"""
		self.status = "Reserved"
		if reservation_details:
			self.reservation_details = reservation_details
		self.save()
		frappe.db.commit()
	
	@frappe.whitelist()
	def get_table_status_summary(self):
		"""Get a summary of table status for dashboard"""
		return {
			"table_name": self.table_name,
			"status": self.status,
			"capacity": self.capacity,
			"location": self.location,
			"current_order": self.current_order,
			"last_updated": self.modified
		}


@frappe.whitelist()
def get_available_tables(location=None, min_capacity=None):
	"""
	Get list of available tables
	
	Args:
		location (str): Filter by location
		min_capacity (int): Minimum capacity required
	
	Returns:
		list: Available tables matching criteria
	"""
	filters = {"status": "Available"}
	
	if location:
		filters["location"] = location
	
	if min_capacity:
		filters["capacity"] = [">=", int(min_capacity)]
	
	tables = frappe.get_all(
		"Restaurant Table",
		filters=filters,
		fields=["name", "table_name", "capacity", "location", "table_type"]
	)
	
	return tables


@frappe.whitelist()
def get_table_occupancy_stats():
	"""Get table occupancy statistics for reporting"""
	stats = frappe.db.sql("""
		SELECT 
			status,
			COUNT(*) as count,
			SUM(capacity) as total_capacity
		FROM `tabRestaurant Table`
		GROUP BY status
	""", as_dict=True)
	
	return stats
