{"actions": [], "allow_rename": 1, "autoname": "field:table_name", "creation": "2024-01-01 00:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["table_name", "capacity", "location", "status", "column_break_5", "table_type", "description", "section_break_8", "current_order", "reservation_details"], "fields": [{"fieldname": "table_name", "fieldtype": "Data", "in_list_view": 1, "label": "Table Name", "reqd": 1, "unique": 1}, {"fieldname": "capacity", "fieldtype": "Int", "in_list_view": 1, "label": "Capacity", "reqd": 1}, {"fieldname": "location", "fieldtype": "Select", "in_list_view": 1, "label": "Location", "options": "Indoor\nOutdoor\nPrivate Room\nBar Area", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Available\nOccupied\nReserved\nOut of Service", "reqd": 1, "default": "Available"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "table_type", "fieldtype": "Select", "label": "Table Type", "options": "Regular\nVIP\nFamily\nCouple"}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Current Status"}, {"fieldname": "current_order", "fieldtype": "Link", "label": "Current Order", "options": "Restaurant Order", "read_only": 1}, {"fieldname": "reservation_details", "fieldtype": "Text", "label": "Reservation Details", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "Restaurant Operations", "name": "Restaurant Table", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Restaurant Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Restaurant Staff", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}