// Copyright (c) 2024, anik and contributors
// For license information, please see license.txt

frappe.ui.form.on('Restaurant Order', {
	refresh: function(frm) {
		// Add custom buttons based on order status
		if (!frm.doc.__islocal) {
			add_order_buttons(frm);
		}
		
		// Set status indicator
		set_order_status_indicator(frm);
		
		// Add order summary
		add_order_summary(frm);
	},
	
	table: function(frm) {
		// Auto-populate table information
		if (frm.doc.table) {
			get_table_info(frm);
		}
	},
	
	status: function(frm) {
		set_order_status_indicator(frm);
	}
});

// Child table events for order items
frappe.ui.form.on('Restaurant Order Item', {
	menu_item: function(frm, cdt, cdn) {
		// Auto-populate item details when menu item is selected
		let row = locals[cdt][cdn];
		if (row.menu_item) {
			frappe.call({
				method: 'frappe.client.get',
				args: {
					doctype: 'Menu Item',
					name: row.menu_item
				},
				callback: function(r) {
					if (r.message) {
						frappe.model.set_value(cdt, cdn, 'item_name', r.message.item_name);
						frappe.model.set_value(cdt, cdn, 'rate', r.message.price);
						calculate_item_amount(frm, cdt, cdn);
					}
				}
			});
		}
	},
	
	quantity: function(frm, cdt, cdn) {
		calculate_item_amount(frm, cdt, cdn);
	},
	
	rate: function(frm, cdt, cdn) {
		calculate_item_amount(frm, cdt, cdn);
	},
	
	items_remove: function(frm) {
		calculate_order_totals(frm);
	}
});

function add_order_buttons(frm) {
	// Add status change buttons
	if (frm.doc.status === 'Draft') {
		frm.add_custom_button(__('Confirm Order'), function() {
			confirm_order(frm);
		}, __('Actions'));
	}
	
	if (frm.doc.status === 'Confirmed') {
		frm.add_custom_button(__('Mark as Preparing'), function() {
			change_order_status(frm, 'mark_as_preparing');
		}, __('Actions'));
	}
	
	if (frm.doc.status === 'Preparing') {
		frm.add_custom_button(__('Mark as Ready'), function() {
			change_order_status(frm, 'mark_as_ready');
		}, __('Actions'));
	}
	
	if (frm.doc.status === 'Ready') {
		frm.add_custom_button(__('Mark as Served'), function() {
			change_order_status(frm, 'mark_as_served');
		}, __('Actions'));
	}
	
	if (frm.doc.status === 'Served') {
		frm.add_custom_button(__('Process Payment'), function() {
			process_payment(frm);
		}, __('Actions'));
	}
	
	// Add item management buttons
	frm.add_custom_button(__('Add Menu Item'), function() {
		add_menu_item_dialog(frm);
	}, __('Items'));
	
	frm.add_custom_button(__('Quick Add Popular Items'), function() {
		show_popular_items(frm);
	}, __('Items'));
	
	// Print button
	frm.add_custom_button(__('Print Order'), function() {
		print_order(frm);
	}, __('Print'));
}

function set_order_status_indicator(frm) {
	let status_colors = {
		'Draft': 'blue',
		'Confirmed': 'orange',
		'Preparing': 'yellow',
		'Ready': 'purple',
		'Served': 'green',
		'Paid': 'green',
		'Cancelled': 'red'
	};
	
	let color = status_colors[frm.doc.status] || 'gray';
	frm.dashboard.set_headline_alert(
		`<div class="indicator ${color}">Order Status: ${frm.doc.status}</div>`
	);
}

function add_order_summary(frm) {
	frappe.call({
		method: 'get_order_summary',
		doc: frm.doc,
		callback: function(r) {
			if (r.message) {
				let summary = r.message;
				let summary_html = `
					<div class="order-summary">
						<h5>Order Summary</h5>
						<div class="row">
							<div class="col-md-6">
								<p><strong>Order ID:</strong> ${summary.order_id}</p>
								<p><strong>Table:</strong> ${summary.table || 'Not assigned'}</p>
								<p><strong>Customer:</strong> ${summary.customer_name || 'Walk-in'}</p>
								<p><strong>Total Items:</strong> ${summary.total_items}</p>
							</div>
							<div class="col-md-6">
								<p><strong>Order Date:</strong> ${frappe.datetime.str_to_user(summary.order_date)}</p>
								<p><strong>Status:</strong> ${summary.status}</p>
								<p><strong>Grand Total:</strong> ${format_currency(summary.grand_total)}</p>
								<p><strong>Payment:</strong> ${summary.payment_status}</p>
							</div>
						</div>
					</div>
				`;
				
				frm.dashboard.add_section(summary_html, __('Summary'));
			}
		}
	});
}

function get_table_info(frm) {
	frappe.call({
		method: 'frappe.client.get',
		args: {
			doctype: 'Restaurant Table',
			name: frm.doc.table
		},
		callback: function(r) {
			if (r.message) {
				let table = r.message;
				frappe.show_alert({
					message: __(`Table ${table.table_name} - Capacity: ${table.capacity}, Location: ${table.location}`),
					indicator: 'blue'
				});
			}
		}
	});
}

function calculate_item_amount(frm, cdt, cdn) {
	let row = locals[cdt][cdn];
	if (row.quantity && row.rate) {
		let amount = row.quantity * row.rate;
		frappe.model.set_value(cdt, cdn, 'amount', amount);
		calculate_order_totals(frm);
	}
}

function calculate_order_totals(frm) {
	let total = 0;
	frm.doc.items.forEach(function(item) {
		if (item.amount) {
			total += item.amount;
		}
	});
	
	frm.set_value('total_amount', total);
	
	// Calculate grand total including tax
	let tax_amount = frm.doc.tax_amount || 0;
	frm.set_value('grand_total', total + tax_amount);
}

function confirm_order(frm) {
	frappe.confirm(
		__('Confirm this order? This will send it to the kitchen.'),
		function() {
			frm.submit();
		}
	);
}

function change_order_status(frm, method) {
	frappe.call({
		method: method,
		doc: frm.doc,
		callback: function(r) {
			if (r.message) {
				frm.reload_doc();
				frappe.show_alert({
					message: __(`Order status changed to ${r.message}`),
					indicator: 'green'
				});
			}
		}
	});
}

function process_payment(frm) {
	frappe.prompt([
		{
			label: 'Payment Method',
			fieldname: 'payment_method',
			fieldtype: 'Select',
			options: 'Cash\nCard\nDigital Wallet\nBank Transfer',
			reqd: 1
		},
		{
			label: 'Amount Received',
			fieldname: 'amount_received',
			fieldtype: 'Currency',
			default: frm.doc.grand_total,
			reqd: 1
		}
	], function(values) {
		frappe.call({
			method: 'mark_as_paid',
			doc: frm.doc,
			args: {
				payment_method: values.payment_method
			},
			callback: function(r) {
				if (r.message) {
					frm.reload_doc();
					frappe.show_alert({
						message: __('Payment processed successfully'),
						indicator: 'green'
					});
					
					// Calculate change if cash payment
					if (values.payment_method === 'Cash' && values.amount_received > frm.doc.grand_total) {
						let change = values.amount_received - frm.doc.grand_total;
						frappe.msgprint({
							title: __('Change Due'),
							message: __(`Change to return: ${format_currency(change)}`),
							indicator: 'blue'
						});
					}
				}
			}
		});
	}, __('Process Payment'));
}

function add_menu_item_dialog(frm) {
	// Create dialog to add menu items
	let dialog = new frappe.ui.Dialog({
		title: __('Add Menu Item'),
		fields: [
			{
				label: 'Menu Item',
				fieldname: 'menu_item',
				fieldtype: 'Link',
				options: 'Menu Item',
				reqd: 1,
				get_query: function() {
					return {
						filters: {
							'is_available': 1
						}
					};
				}
			},
			{
				label: 'Quantity',
				fieldname: 'quantity',
				fieldtype: 'Int',
				default: 1,
				reqd: 1
			},
			{
				label: 'Special Instructions',
				fieldname: 'special_instructions',
				fieldtype: 'Text'
			}
		],
		primary_action_label: __('Add Item'),
		primary_action: function(values) {
			frappe.call({
				method: 'add_item',
				doc: frm.doc,
				args: values,
				callback: function(r) {
					if (r.message) {
						frm.reload_doc();
						dialog.hide();
						frappe.show_alert({
							message: __('Item added to order'),
							indicator: 'green'
						});
					}
				}
			});
		}
	});
	
	dialog.show();
}

function show_popular_items(frm) {
	frappe.call({
		method: 'restaurant_management.menu_management.doctype.menu_item.menu_item.get_popular_items',
		args: {
			limit: 10
		},
		callback: function(r) {
			if (r.message && r.message.length > 0) {
				let items_html = '<div class="popular-items">';
				r.message.forEach(function(item) {
					items_html += `
						<div class="item-card" data-item="${item.name}">
							<h6>${item.item_name}</h6>
							<p>Category: ${item.category}</p>
							<p>Price: ${format_currency(item.price)}</p>
							<button class="btn btn-sm btn-primary add-item-btn">Add to Order</button>
						</div>
					`;
				});
				items_html += '</div>';
				
				let dialog = frappe.msgprint({
					title: __('Popular Items'),
					message: items_html,
					wide: true
				});
				
				// Add click handlers for add buttons
				$(dialog.$wrapper).find('.add-item-btn').click(function() {
					let item_name = $(this).closest('.item-card').data('item');
					frappe.call({
						method: 'add_item',
						doc: frm.doc,
						args: {
							menu_item: item_name,
							quantity: 1
						},
						callback: function(r) {
							if (r.message) {
								frm.reload_doc();
								frappe.show_alert({
									message: __('Item added to order'),
									indicator: 'green'
								});
							}
						}
					});
				});
			}
		}
	});
}

function print_order(frm) {
	// This would typically integrate with a print format
	frappe.msgprint({
		title: __('Print Order'),
		message: __('Order sent to printer'),
		indicator: 'blue'
	});
}

// Utility function to format currency
function format_currency(amount) {
	return new Intl.NumberFormat('en-US', {
		style: 'currency',
		currency: 'USD'
	}).format(amount || 0);
}
