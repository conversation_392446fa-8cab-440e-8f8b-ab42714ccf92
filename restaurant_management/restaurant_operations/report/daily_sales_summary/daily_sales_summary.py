# Copyright (c) 2024, anik and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import getdate, flt


def execute(filters=None):
	"""
	Daily Sales Summary Report
	
	This report provides a comprehensive overview of daily sales including:
	- Total orders and revenue
	- Payment method breakdown
	- Table utilization
	- Popular items
	"""
	columns = get_columns()
	data = get_data(filters)
	chart = get_chart_data(data)
	summary = get_summary(data)
	
	return columns, data, None, chart, summary


def get_columns():
	"""Define report columns"""
	return [
		{
			"fieldname": "date",
			"label": _("Date"),
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "total_orders",
			"label": _("Total Orders"),
			"fieldtype": "Int",
			"width": 120
		},
		{
			"fieldname": "completed_orders",
			"label": _("Completed Orders"),
			"fieldtype": "Int",
			"width": 140
		},
		{
			"fieldname": "cancelled_orders",
			"label": _("Cancelled Orders"),
			"fieldtype": "Int",
			"width": 140
		},
		{
			"fieldname": "total_revenue",
			"label": _("Total Revenue"),
			"fieldtype": "Currency",
			"width": 130
		},
		{
			"fieldname": "avg_order_value",
			"label": _("Avg Order Value"),
			"fieldtype": "Currency",
			"width": 140
		},
		{
			"fieldname": "cash_sales",
			"label": _("Cash Sales"),
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "card_sales",
			"label": _("Card Sales"),
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "digital_wallet_sales",
			"label": _("Digital Wallet"),
			"fieldtype": "Currency",
			"width": 130
		}
	]


def get_data(filters):
	"""Get report data"""
	conditions = get_conditions(filters)
	
	data = frappe.db.sql(f"""
		SELECT 
			DATE(order_date) as date,
			COUNT(*) as total_orders,
			COUNT(CASE WHEN status = 'Paid' THEN 1 END) as completed_orders,
			COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as cancelled_orders,
			SUM(CASE WHEN status = 'Paid' THEN grand_total ELSE 0 END) as total_revenue,
			AVG(CASE WHEN status = 'Paid' THEN grand_total ELSE NULL END) as avg_order_value,
			SUM(CASE WHEN payment_method = 'Cash' AND status = 'Paid' THEN grand_total ELSE 0 END) as cash_sales,
			SUM(CASE WHEN payment_method = 'Card' AND status = 'Paid' THEN grand_total ELSE 0 END) as card_sales,
			SUM(CASE WHEN payment_method = 'Digital Wallet' AND status = 'Paid' THEN grand_total ELSE 0 END) as digital_wallet_sales
		FROM `tabRestaurant Order`
		WHERE docstatus != 2 {conditions}
		GROUP BY DATE(order_date)
		ORDER BY date DESC
	""", filters, as_dict=True)
	
	# Format the data
	for row in data:
		row.total_revenue = flt(row.total_revenue, 2)
		row.avg_order_value = flt(row.avg_order_value, 2)
		row.cash_sales = flt(row.cash_sales, 2)
		row.card_sales = flt(row.card_sales, 2)
		row.digital_wallet_sales = flt(row.digital_wallet_sales, 2)
	
	return data


def get_conditions(filters):
	"""Build SQL conditions based on filters"""
	conditions = ""
	
	if filters.get("from_date"):
		conditions += " AND DATE(order_date) >= %(from_date)s"
	
	if filters.get("to_date"):
		conditions += " AND DATE(order_date) <= %(to_date)s"
	
	if filters.get("table"):
		conditions += " AND table = %(table)s"
	
	return conditions


def get_chart_data(data):
	"""Generate chart data for visualization"""
	if not data:
		return None
	
	# Revenue trend chart
	dates = [row.date.strftime("%Y-%m-%d") for row in data[-7:]]  # Last 7 days
	revenues = [row.total_revenue for row in data[-7:]]
	
	chart = {
		"data": {
			"labels": dates,
			"datasets": [
				{
					"name": "Daily Revenue",
					"values": revenues
				}
			]
		},
		"type": "line",
		"height": 300,
		"colors": ["#7cd6fd"]
	}
	
	return chart


def get_summary(data):
	"""Generate summary statistics"""
	if not data:
		return []
	
	total_orders = sum(row.total_orders for row in data)
	total_revenue = sum(row.total_revenue for row in data)
	total_completed = sum(row.completed_orders for row in data)
	total_cancelled = sum(row.cancelled_orders for row in data)
	
	avg_daily_revenue = total_revenue / len(data) if data else 0
	completion_rate = (total_completed / total_orders * 100) if total_orders > 0 else 0
	
	summary = [
		{
			"value": total_orders,
			"label": _("Total Orders"),
			"datatype": "Int"
		},
		{
			"value": total_revenue,
			"label": _("Total Revenue"),
			"datatype": "Currency"
		},
		{
			"value": avg_daily_revenue,
			"label": _("Avg Daily Revenue"),
			"datatype": "Currency"
		},
		{
			"value": f"{completion_rate:.1f}%",
			"label": _("Order Completion Rate"),
			"datatype": "Data"
		}
	]
	
	return summary


# Additional utility functions for the report
@frappe.whitelist()
def get_top_selling_items(from_date=None, to_date=None, limit=10):
	"""Get top selling menu items for the period"""
	conditions = ""
	filters = {}
	
	if from_date:
		conditions += " AND DATE(ro.order_date) >= %(from_date)s"
		filters["from_date"] = from_date
	
	if to_date:
		conditions += " AND DATE(ro.order_date) <= %(to_date)s"
		filters["to_date"] = to_date
	
	top_items = frappe.db.sql(f"""
		SELECT 
			roi.menu_item,
			roi.item_name,
			SUM(roi.quantity) as total_quantity,
			SUM(roi.amount) as total_amount,
			COUNT(DISTINCT ro.name) as order_count
		FROM `tabRestaurant Order Item` roi
		INNER JOIN `tabRestaurant Order` ro ON roi.parent = ro.name
		WHERE ro.docstatus != 2 AND ro.status = 'Paid' {conditions}
		GROUP BY roi.menu_item, roi.item_name
		ORDER BY total_quantity DESC
		LIMIT %(limit)s
	""", dict(filters, limit=limit), as_dict=True)
	
	return top_items


@frappe.whitelist()
def get_table_utilization(from_date=None, to_date=None):
	"""Get table utilization statistics"""
	conditions = ""
	filters = {}
	
	if from_date:
		conditions += " AND DATE(order_date) >= %(from_date)s"
		filters["from_date"] = from_date
	
	if to_date:
		conditions += " AND DATE(order_date) <= %(to_date)s"
		filters["to_date"] = to_date
	
	utilization = frappe.db.sql(f"""
		SELECT 
			ro.table,
			rt.table_name,
			rt.capacity,
			rt.location,
			COUNT(*) as total_orders,
			SUM(ro.grand_total) as total_revenue,
			AVG(ro.grand_total) as avg_order_value
		FROM `tabRestaurant Order` ro
		INNER JOIN `tabRestaurant Table` rt ON ro.table = rt.name
		WHERE ro.docstatus != 2 AND ro.status = 'Paid' {conditions}
		GROUP BY ro.table, rt.table_name, rt.capacity, rt.location
		ORDER BY total_revenue DESC
	""", filters, as_dict=True)
	
	return utilization
