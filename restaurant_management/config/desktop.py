"""
Desktop configuration for Restaurant Management
This file defines the desktop icons and workspace layout
"""

from frappe import _


def get_data():
	"""Return desktop configuration"""
	return [
		{
			"module_name": "Restaurant Operations",
			"category": "Modules",
			"label": _("Restaurant Operations"),
			"color": "#FF6B6B",
			"icon": "fa fa-cutlery",
			"type": "module",
			"description": _("Manage tables, orders, and reservations")
		},
		{
			"module_name": "Menu Management",
			"category": "Modules", 
			"label": _("Menu Management"),
			"color": "#4ECDC4",
			"icon": "fa fa-book",
			"type": "module",
			"description": _("Manage menu items, categories, and pricing")
		}
	]
