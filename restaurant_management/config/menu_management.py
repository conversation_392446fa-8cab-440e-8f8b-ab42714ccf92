"""
Menu Management Module Configuration
This file defines the module structure and navigation
"""

from frappe import _


def get_data():
	"""Return module configuration"""
	return [
		{
			"label": _("Menu Management"),
			"items": [
				{
					"type": "doctype",
					"name": "Menu Item",
					"label": _("Menu Item"),
					"description": _("Manage menu items and pricing")
				}
			]
		},
		{
			"label": _("Reports"),
			"items": [
				{
					"type": "report",
					"name": "Menu Performance",
					"label": _("Menu Performance"),
					"doctype": "Menu Item",
					"is_query_report": True
				}
			]
		},
		{
			"label": _("Tools"),
			"items": [
				{
					"type": "page",
					"name": "menu-builder",
					"label": _("Menu Builder"),
					"description": _("Visual menu builder tool")
				}
			]
		}
	]
