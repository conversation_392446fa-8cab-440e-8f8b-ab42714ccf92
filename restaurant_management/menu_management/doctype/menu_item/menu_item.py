# Copyright (c) 2024, anik and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class MenuItem(Document):
	"""
	Menu Item DocType Controller

	This class handles the business logic for menu items including:
	- Price calculations and profit margins
	- Availability management
	- Menu categorization
	"""

	def validate(self):
		"""Validate menu item data before saving"""
		self.validate_price()
		self.calculate_profit_margin()
		self.validate_preparation_time()

	def validate_price(self):
		"""Ensure price is valid"""
		if self.price <= 0:
			frappe.throw("Menu item price must be greater than 0")

		if self.cost_price and self.cost_price >= self.price:
			frappe.msgprint(
				"Cost price is equal to or higher than selling price. Please review pricing.",
				alert=True
			)

	def calculate_profit_margin(self):
		"""Calculate profit margin based on cost and selling price"""
		if self.cost_price and self.price:
			profit = self.price - self.cost_price
			self.profit_margin = (profit / self.price) * 100
		else:
			self.profit_margin = 0

	def validate_preparation_time(self):
		"""Validate preparation time"""
		if self.preparation_time and self.preparation_time <= 0:
			frappe.throw("Preparation time must be greater than 0 minutes")

		if self.preparation_time and self.preparation_time > 120:
			frappe.msgprint(
				"Preparation time is very high (>2 hours). Please verify.",
				alert=True
			)

	def before_save(self):
		"""Actions to perform before saving"""
		# Ensure item name is properly formatted
		if self.item_name:
			self.item_name = self.item_name.title()

	def on_update(self):
		"""Actions to perform after updating"""
		# Log availability changes
		if self.has_value_changed("is_available"):
			status = "available" if self.is_available else "unavailable"
			self.add_comment("Info", f"Menu item marked as {status}")

	@frappe.whitelist()
	def toggle_availability(self):
		"""Toggle item availability"""
		self.is_available = not self.is_available
		self.save()
		return self.is_available

	@frappe.whitelist()
	def get_item_details(self):
		"""Get comprehensive item details for display"""
		return {
			"item_name": self.item_name,
			"category": self.category,
			"price": self.price,
			"description": self.description,
			"is_available": self.is_available,
			"is_vegetarian": self.is_vegetarian,
			"is_spicy": self.is_spicy,
			"preparation_time": self.preparation_time,
			"calories": self.calories,
			"allergens": self.allergens,
			"image": self.image
		}


@frappe.whitelist()
def get_menu_by_category(category=None, available_only=True):
	"""
	Get menu items by category

	Args:
		category (str): Filter by category
		available_only (bool): Only return available items

	Returns:
		list: Menu items matching criteria
	"""
	filters = {}

	if category:
		filters["category"] = category

	if available_only:
		filters["is_available"] = 1

	items = frappe.get_all(
		"Menu Item",
		filters=filters,
		fields=[
			"name", "item_name", "category", "price", "description",
			"is_vegetarian", "is_spicy", "preparation_time", "image"
		],
		order_by="category, item_name"
	)

	return items


@frappe.whitelist()
def get_menu_categories():
	"""Get all menu categories with item counts"""
	categories = frappe.db.sql("""
		SELECT
			category,
			COUNT(*) as total_items,
			COUNT(CASE WHEN is_available = 1 THEN 1 END) as available_items,
			AVG(price) as avg_price
		FROM `tabMenu Item`
		GROUP BY category
		ORDER BY category
	""", as_dict=True)

	return categories


@frappe.whitelist()
def search_menu_items(search_term, filters=None):
	"""
	Search menu items by name, description, or ingredients

	Args:
		search_term (str): Search term
		filters (dict): Additional filters

	Returns:
		list: Matching menu items
	"""
	if not search_term:
		return []

	search_term = f"%{search_term}%"

	base_filters = {"is_available": 1}
	if filters:
		base_filters.update(filters)

	# Build filter conditions
	filter_conditions = []
	filter_values = []

	for field, value in base_filters.items():
		filter_conditions.append(f"`{field}` = %s")
		filter_values.append(value)

	where_clause = " AND ".join(filter_conditions) if filter_conditions else "1=1"

	items = frappe.db.sql(f"""
		SELECT
			name, item_name, category, price, description,
			is_vegetarian, is_spicy, preparation_time, image
		FROM `tabMenu Item`
		WHERE ({where_clause})
		AND (
			item_name LIKE %s
			OR description LIKE %s
			OR ingredients LIKE %s
		)
		ORDER BY item_name
	""", filter_values + [search_term, search_term, search_term], as_dict=True)

	return items


@frappe.whitelist()
def get_popular_items(limit=10):
	"""Get popular menu items based on order frequency"""
	# This would typically join with order items table
	# For now, return items sorted by creation date as a placeholder
	items = frappe.get_all(
		"Menu Item",
		filters={"is_available": 1},
		fields=["name", "item_name", "category", "price", "image"],
		order_by="creation desc",
		limit=limit
	)
	return items
