// Copyright (c) 2024, anik and contributors
// For license information, please see license.txt

frappe.ui.form.on('Menu Item', {
	refresh: function(frm) {
		// Add custom buttons
		if (!frm.doc.__islocal) {
			add_menu_item_buttons(frm);
		}
		
		// Set availability indicator
		set_availability_indicator(frm);
		
		// Add quick stats
		add_menu_item_stats(frm);
	},
	
	price: function(frm) {
		calculate_profit_margin(frm);
	},
	
	cost_price: function(frm) {
		calculate_profit_margin(frm);
	},
	
	is_available: function(frm) {
		set_availability_indicator(frm);
	},
	
	category: function(frm) {
		// Auto-suggest preparation time based on category
		suggest_preparation_time(frm);
	}
});

function add_menu_item_buttons(frm) {
	// Toggle availability button
	let availability_label = frm.doc.is_available ? __('Mark Unavailable') : __('Mark Available');
	frm.add_custom_button(availability_label, function() {
		toggle_availability(frm);
	}, __('Actions'));
	
	// View item details button
	frm.add_custom_button(__('View Details'), function() {
		show_item_details(frm);
	}, __('Actions'));
	
	// Duplicate item button
	frm.add_custom_button(__('Duplicate Item'), function() {
		duplicate_menu_item(frm);
	}, __('Actions'));
}

function set_availability_indicator(frm) {
	let color = frm.doc.is_available ? 'green' : 'red';
	let status = frm.doc.is_available ? 'Available' : 'Unavailable';
	
	frm.dashboard.set_headline_alert(
		`<div class="indicator ${color}">Status: ${status}</div>`
	);
}

function add_menu_item_stats(frm) {
	// Add statistics section
	let stats_html = `
		<div class="menu-item-stats">
			<h5>Item Statistics</h5>
			<div class="row">
				<div class="col-md-4">
					<div class="stat-card">
						<h6>Price</h6>
						<p class="stat-value">${format_currency(frm.doc.price)}</p>
					</div>
				</div>
				<div class="col-md-4">
					<div class="stat-card">
						<h6>Profit Margin</h6>
						<p class="stat-value">${frm.doc.profit_margin || 0}%</p>
					</div>
				</div>
				<div class="col-md-4">
					<div class="stat-card">
						<h6>Prep Time</h6>
						<p class="stat-value">${frm.doc.preparation_time || 0} min</p>
					</div>
				</div>
			</div>
		</div>
		<style>
			.stat-card {
				text-align: center;
				padding: 10px;
				border: 1px solid #ddd;
				border-radius: 5px;
				margin: 5px;
			}
			.stat-value {
				font-size: 18px;
				font-weight: bold;
				color: #333;
			}
		</style>
	`;
	
	frm.dashboard.add_section(stats_html, __('Statistics'));
}

function calculate_profit_margin(frm) {
	if (frm.doc.price && frm.doc.cost_price) {
		let profit = frm.doc.price - frm.doc.cost_price;
		let margin = (profit / frm.doc.price) * 100;
		frm.set_value('profit_margin', margin);
		
		// Show warning if margin is low
		if (margin < 20) {
			frappe.msgprint({
				title: __('Low Profit Margin'),
				message: __('The profit margin is below 20%. Consider reviewing the pricing.'),
				indicator: 'orange'
			});
		}
	}
}

function suggest_preparation_time(frm) {
	// Suggest preparation time based on category
	let time_suggestions = {
		'Appetizer': 10,
		'Salad': 8,
		'Soup': 12,
		'Main Course': 25,
		'Side Dish': 15,
		'Dessert': 10,
		'Beverage': 3
	};
	
	if (frm.doc.category && !frm.doc.preparation_time) {
		let suggested_time = time_suggestions[frm.doc.category];
		if (suggested_time) {
			frm.set_value('preparation_time', suggested_time);
			frappe.show_alert({
				message: __(`Suggested preparation time: ${suggested_time} minutes`),
				indicator: 'blue'
			});
		}
	}
}

function toggle_availability(frm) {
	frappe.call({
		method: 'toggle_availability',
		doc: frm.doc,
		callback: function(r) {
			if (r.message !== undefined) {
				frm.reload_doc();
				let status = r.message ? 'available' : 'unavailable';
				frappe.show_alert({
					message: __(`Item marked as ${status}`),
					indicator: r.message ? 'green' : 'red'
				});
			}
		}
	});
}

function show_item_details(frm) {
	frappe.call({
		method: 'get_item_details',
		doc: frm.doc,
		callback: function(r) {
			if (r.message) {
				let details = r.message;
				let dialog_html = `
					<div class="item-details">
						<h4>${details.item_name}</h4>
						<p><strong>Category:</strong> ${details.category}</p>
						<p><strong>Price:</strong> ${format_currency(details.price)}</p>
						<p><strong>Description:</strong> ${details.description || 'No description'}</p>
						<div class="row">
							<div class="col-md-6">
								<p><strong>Vegetarian:</strong> ${details.is_vegetarian ? 'Yes' : 'No'}</p>
								<p><strong>Spicy:</strong> ${details.is_spicy ? 'Yes' : 'No'}</p>
							</div>
							<div class="col-md-6">
								<p><strong>Prep Time:</strong> ${details.preparation_time || 0} minutes</p>
								<p><strong>Calories:</strong> ${details.calories || 'Not specified'}</p>
							</div>
						</div>
						${details.allergens ? `<p><strong>Allergens:</strong> ${details.allergens}</p>` : ''}
					</div>
				`;
				
				frappe.msgprint({
					title: __('Item Details'),
					message: dialog_html,
					wide: true
				});
			}
		}
	});
}

function duplicate_menu_item(frm) {
	frappe.confirm(
		__('Create a duplicate of this menu item?'),
		function() {
			frappe.call({
				method: 'frappe.client.copy_doc',
				args: {
					dt: 'Menu Item',
					dn: frm.doc.name
				},
				callback: function(r) {
					if (r.message) {
						frappe.set_route('Form', 'Menu Item', r.message.name);
					}
				}
			});
		}
	);
}

// Utility function to format currency
function format_currency(amount) {
	return new Intl.NumberFormat('en-US', {
		style: 'currency',
		currency: 'USD'
	}).format(amount || 0);
}
