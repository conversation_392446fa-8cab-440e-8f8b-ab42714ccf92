{"actions": [], "allow_rename": 1, "autoname": "field:item_name", "creation": "2024-01-01 00:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_name", "category", "price", "column_break_4", "is_available", "is_vegetarian", "is_spicy", "section_break_8", "description", "ingredients", "column_break_11", "image", "section_break_13", "preparation_time", "calories", "allergens", "column_break_17", "cost_price", "profit_margin", "section_break_20", "tags"], "fields": [{"fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Name", "reqd": 1, "unique": 1}, {"fieldname": "category", "fieldtype": "Select", "in_list_view": 1, "label": "Category", "options": "Appetizer\nMain Course\nDessert\nBeverage\nSalad\nSoup\nSide Dish", "reqd": 1}, {"fieldname": "price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Price", "reqd": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"default": "1", "fieldname": "is_available", "fieldtype": "Check", "in_list_view": 1, "label": "Available"}, {"default": "0", "fieldname": "is_vegetarian", "fieldtype": "Check", "label": "Vegetarian"}, {"default": "0", "fieldname": "is_spicy", "fieldtype": "Check", "label": "Spicy"}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Details"}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}, {"fieldname": "ingredients", "fieldtype": "Long Text", "label": "Ingredients"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "image", "fieldtype": "Attach Image", "label": "Image"}, {"fieldname": "section_break_13", "fieldtype": "Section Break", "label": "Additional Information"}, {"fieldname": "preparation_time", "fieldtype": "Int", "label": "Preparation Time (minutes)"}, {"fieldname": "calories", "fieldtype": "Int", "label": "Calories"}, {"fieldname": "allergens", "fieldtype": "Small Text", "label": "Allergens"}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"fieldname": "cost_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Cost Price"}, {"fieldname": "profit_margin", "fieldtype": "Percent", "label": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "section_break_20", "fieldtype": "Section Break", "label": "Tags"}, {"fieldname": "tags", "fieldtype": "Table MultiSelect", "label": "Tags", "options": "<PERSON><PERSON> Item Tag"}], "image_field": "image", "index_web_pages_for_search": 1, "links": [], "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "Menu Management", "name": "<PERSON><PERSON>", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Restaurant Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Restaurant Staff", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "item_name", "track_changes": 1}